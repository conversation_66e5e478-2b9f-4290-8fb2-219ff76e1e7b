optree-0.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
optree-0.17.0.dist-info/METADATA,sha256=LKD57UtdJvkW08X6j0xkzvlDtSPNQyfy1UDuxD2wGms,34026
optree-0.17.0.dist-info/RECORD,,
optree-0.17.0.dist-info/WHEEL,sha256=JLOMsP7F5qtkAkINx5UnzbFguf8CqZeraV8o04b0I8I,101
optree-0.17.0.dist-info/licenses/LICENSE,sha256=_mSB_S6V5JPgawQiTVaP3Ajs2vt5QopxMMIplYrUey8,11370
optree-0.17.0.dist-info/top_level.txt,sha256=wxgWsTkpSlcz3dX9fMJb8ShwAlFdBUpvrlMRYE31_Po,7
optree/_C.cp311-win_amd64.pyd,sha256=DGJg8WxzRNCYP8-eMcSZtpftR4Ql0fCXwSGzut515tI,565248
optree/_C.pyi,sha256=RM5jD9-VbczvtP_g9vnY310UT2xO6D__GNUbBmawCP4,6931
optree/__init__.py,sha256=B3ErnLQQRpCoa1MnWoO4dIrW09iiDVtPKVZvL-rcs_E,6694
optree/__pycache__/__init__.cpython-311.pyc,,
optree/__pycache__/accessor.cpython-311.pyc,,
optree/__pycache__/accessors.cpython-311.pyc,,
optree/__pycache__/dataclasses.cpython-311.pyc,,
optree/__pycache__/functools.cpython-311.pyc,,
optree/__pycache__/ops.cpython-311.pyc,,
optree/__pycache__/pytree.cpython-311.pyc,,
optree/__pycache__/registry.cpython-311.pyc,,
optree/__pycache__/treespec.cpython-311.pyc,,
optree/__pycache__/typing.cpython-311.pyc,,
optree/__pycache__/utils.cpython-311.pyc,,
optree/__pycache__/version.cpython-311.pyc,,
optree/accessor.py,sha256=WBt8lik5Rm1KlJ2DYuOHqtdCpsbVIe_6tS7mgq4yLco,1440
optree/accessors.py,sha256=S0ixH7hU8rorNZxGMozMhSAibmnU3Y7pVtjajYt7xBE,14452
optree/dataclasses.py,sha256=P9NHn5vpb4xw_uUv1xuo3KGMlw5Ios55KJAL689LWGA,20143
optree/functools.py,sha256=q8y9GEX31iQQBed-adOr7s_IDTEx7UzCqjL5YEt26aw,6416
optree/integration/__init__.py,sha256=dXLSuofEelG29vE4Id_na7VLZ_BNvz13mK4yM8TioGs,1495
optree/integration/__pycache__/__init__.cpython-311.pyc,,
optree/integration/__pycache__/jax.cpython-311.pyc,,
optree/integration/__pycache__/numpy.cpython-311.pyc,,
optree/integration/__pycache__/torch.cpython-311.pyc,,
optree/integration/jax.py,sha256=TAAVeIL8DxtEXT7XwGtMvHjmd80rdAK1GbvGJgkmLvA,1469
optree/integration/numpy.py,sha256=0ZZJ1qKlyAjfYTICv2yQtXTuKzycLe0P-8kgCBEG8TU,1481
optree/integration/torch.py,sha256=pMxUkRFWcDxiyLuCNqL8aNzDaJBGGDw3M7u30i5h-w4,1483
optree/integrations/__init__.py,sha256=QMC3xCNnpWColXkPIIY-TwtznpQF3G27NDyXLNMYxv8,1587
optree/integrations/__pycache__/__init__.cpython-311.pyc,,
optree/integrations/__pycache__/jax.cpython-311.pyc,,
optree/integrations/__pycache__/numpy.cpython-311.pyc,,
optree/integrations/__pycache__/torch.cpython-311.pyc,,
optree/integrations/jax.py,sha256=tNC5b5MOoEmCp8tYFLUrAI2ACyKysr6n_LZ9YP2MFdo,10824
optree/integrations/numpy.py,sha256=0MHih0sXTXV_5SpjD4L7g0VB-qbW-Ez3tlnmTPwaDiQ,7800
optree/integrations/torch.py,sha256=gnkLN7htGidUd2vb6Q4HdzPPZshVyWFaNMoN0cF0EaU,8120
optree/ops.py,sha256=sDdO_rWQb-I11UVruHGvFQWSsy9jIeJ8bQV-IKJNJa8,156662
optree/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
optree/pytree.py,sha256=tE6t7GDOiVueO06E-5FVvqFDEekP8QtPkQ4a4MMzwyE,15157
optree/registry.py,sha256=gUULpYIh2TjD4smPw6ApEbZZCtgiNzXayy-0QHa3qRU,30227
optree/treespec.py,sha256=CWpF1E2LCe-4z14HpHcrOGkwG-kokmvROtJmMxbBRys,1923
optree/typing.py,sha256=ZmdWJQ7wlKHQelgetjNLNtevlu0FZ_s9YgwgL-ESqcM,19550
optree/utils.py,sha256=7trPsYmQqEPMsErFC5_sIo5VkvHWT57C_tHq2-rZ44g,3498
optree/version.py,sha256=GqCiAQNb7VihPdxVR_UwlfaY1dOopPoAmPrGnePM8R0,2101

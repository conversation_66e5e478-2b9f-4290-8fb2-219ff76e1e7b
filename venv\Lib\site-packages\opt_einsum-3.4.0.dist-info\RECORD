opt_einsum-3.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
opt_einsum-3.4.0.dist-info/METADATA,sha256=n2eRFAxeG-TxsYk2XmMS1xGh6q6zmbXdZ0H3pL2Z0Ds,6345
opt_einsum-3.4.0.dist-info/RECORD,,
opt_einsum-3.4.0.dist-info/WHEEL,sha256=1yFddiXMmvYK7QYTqtRNtX66WJ0Mz8PYEiEUoOUUxRY,87
opt_einsum-3.4.0.dist-info/licenses/LICENSE,sha256=eyZpGnyO5Ir0JaU8uuqryao6hOouk_5HoezFUMHedts,1080
opt_einsum/__init__.py,sha256=LPIPWo9YglJpU7soEk8We_APdsvZX4p5DfDzXmbC3f8,834
opt_einsum/__pycache__/__init__.cpython-311.pyc,,
opt_einsum/__pycache__/_version.cpython-311.pyc,,
opt_einsum/__pycache__/blas.cpython-311.pyc,,
opt_einsum/__pycache__/contract.cpython-311.pyc,,
opt_einsum/__pycache__/helpers.cpython-311.pyc,,
opt_einsum/__pycache__/parser.cpython-311.pyc,,
opt_einsum/__pycache__/path_random.cpython-311.pyc,,
opt_einsum/__pycache__/paths.cpython-311.pyc,,
opt_einsum/__pycache__/sharing.cpython-311.pyc,,
opt_einsum/__pycache__/testing.cpython-311.pyc,,
opt_einsum/__pycache__/typing.cpython-311.pyc,,
opt_einsum/_version.py,sha256=YWGqQYvejjlymmjzg4jncyBgDC760jlRmyon_Rd-2uQ,411
opt_einsum/backends/__init__.py,sha256=bEAsIERI4AgTwEKxwzGQFDabNnIqiqGpJkenGm4mSQw,614
opt_einsum/backends/__pycache__/__init__.cpython-311.pyc,,
opt_einsum/backends/__pycache__/cupy.cpython-311.pyc,,
opt_einsum/backends/__pycache__/dispatch.cpython-311.pyc,,
opt_einsum/backends/__pycache__/jax.cpython-311.pyc,,
opt_einsum/backends/__pycache__/object_arrays.cpython-311.pyc,,
opt_einsum/backends/__pycache__/tensorflow.cpython-311.pyc,,
opt_einsum/backends/__pycache__/theano.cpython-311.pyc,,
opt_einsum/backends/__pycache__/torch.cpython-311.pyc,,
opt_einsum/backends/cupy.py,sha256=ha9l1kZxL84zOtAAgv4idI5wQv9a1HOlnadMVcy855g,950
opt_einsum/backends/dispatch.py,sha256=MR_8pzT0i9HdwKn-eklIk43seLUQbnvWM9otF4JTuto,4887
opt_einsum/backends/jax.py,sha256=4YFucG53tR-QrzbKO_N47yn2Kc1xPp_9jLjZIU_lRUc,1085
opt_einsum/backends/object_arrays.py,sha256=W91g6HfwDAHFAG8nIsLsjJAy75UuBno9AMeSb8wYF-0,2016
opt_einsum/backends/tensorflow.py,sha256=OgbLpjGyDxeFUcYjJUoVFb8bSyihDjPM0kUV6uEniJA,3891
opt_einsum/backends/theano.py,sha256=fBJaL2FOnx9_B7js4o0gXy3g6dzOdr_fT9kqXkSCCJM,1698
opt_einsum/backends/torch.py,sha256=3v1033W8XyjI4OIqCMvJKZsSP1xKHR-8-_RJRXVUAvI,3583
opt_einsum/blas.py,sha256=sn3kevdTgN6xYUxLo0QBQqXZ47tUYAQgMEeHJt0GN_c,3500
opt_einsum/contract.py,sha256=pCrV7YPgkD7pRY0KiXOYbGuywSCY-cYMyyj57Hr58PI,40341
opt_einsum/helpers.py,sha256=Y1PHxBEu49BEStlym3u8ak8xU3ycwBVu4CUFZ7qiwAA,4270
opt_einsum/parser.py,sha256=PoUJF5YtYlS-C53CH74ewXJ6pla7KWinnSqjOnBQtYU,13269
opt_einsum/path_random.py,sha256=2vkE2oshUXHlW6gtq8bcnflfVfHVaxBSYvkA9wTDvUI,14356
opt_einsum/paths.py,sha256=xqvuBrXX7Xwe22D7L1OmhUt8WUmgh4ZFdvSRPwXMzMw,51711
opt_einsum/sharing.py,sha256=PvWhN59XlvtKbQI6ziYK5lha2HTIvPzypzUxkxi5kHw,6914
opt_einsum/testing.py,sha256=MYJcR1qrEyqIkXRSdIalpu5B53cSAR59Ue2-JMnsfJg,6352
opt_einsum/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opt_einsum/tests/__pycache__/__init__.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_backends.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_blas.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_contract.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_edge_cases.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_input.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_parser.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_paths.cpython-311.pyc,,
opt_einsum/tests/__pycache__/test_sharing.cpython-311.pyc,,
opt_einsum/tests/test_backends.py,sha256=XcL6ifV4mxUzVAcqg0Yaxi3fY-LhvDXH2caiPVTmkKQ,15562
opt_einsum/tests/test_blas.py,sha256=r598QtkCxqXIk93eZQvvoWDMM2qgsWY4hT9_iAryU7o,3590
opt_einsum/tests/test_contract.py,sha256=Gti0GrEWdUg-DSCr2BTZNgYMHDcg5FdXmpjps95IYmE,8589
opt_einsum/tests/test_edge_cases.py,sha256=2zMNMK3YqOhEYxQu937qk_csKxUCZEzEXeW_fdcieZQ,5283
opt_einsum/tests/test_input.py,sha256=KlWo7SSfFsqymeTVpUQ2PsPqbIudccmkFsOSELNNqmI,8275
opt_einsum/tests/test_parser.py,sha256=t0oqBfPEHuVEiQHcjJJDYeTwiwmUVF-Vs3pgi9Mu8T8,2085
opt_einsum/tests/test_paths.py,sha256=aSKPJgABCjc0bX76dGwVoIwPHKMr-_iJD3by3wiFdEE,19461
opt_einsum/tests/test_sharing.py,sha256=hj3PBxbqW8TYzuxNgodpgbPvj3mwB9uCycTyxQbdef4,13125
opt_einsum/typing.py,sha256=Bsc-mCCegpgRSRuMARt4F3fPlhCRhvgg5RGaINYuVR0,937

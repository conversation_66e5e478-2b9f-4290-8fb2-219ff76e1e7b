h5py-3.14.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
h5py-3.14.0.dist-info/METADATA,sha256=vXGbOBWjfOMQVNg95SxzNNeLOpG1AXXPrivaNGOuK6Q,2732
h5py-3.14.0.dist-info/RECORD,,
h5py-3.14.0.dist-info/WHEEL,sha256=JLOMsP7F5qtkAkINx5UnzbFguf8CqZeraV8o04b0I8I,101
h5py-3.14.0.dist-info/licenses/LICENSE,sha256=t3O8fPA4Y3p_2Ah2SMwo4F8PJNU3cNGCcUekCh0mirU,1550
h5py-3.14.0.dist-info/licenses/licenses/hdf5.txt,sha256=18GSR0QzZjO7qsViYQerVclhcVq8n3F0REPkOk2z8ZQ,3835
h5py-3.14.0.dist-info/licenses/licenses/license.txt,sha256=mmMzr79cORmU_j6fUkGsy4wioj61Cy5-mwDqV9zPKdA,1773
h5py-3.14.0.dist-info/licenses/licenses/pytables.txt,sha256=Y1phPxANfuAZRvpSQT9hLP5AKwH1zBOcIhiY8l0EsFc,1673
h5py-3.14.0.dist-info/licenses/licenses/python.txt,sha256=9E77vawyMmRYVIQ77I6KIGYhqrbF1cw-TD6Diiqm0Ms,2547
h5py-3.14.0.dist-info/licenses/licenses/stdint.txt,sha256=MLdpQq3P4lF6pW56gWRLQSoqKdFKxfDSmaVn7FSileU,1419
h5py-3.14.0.dist-info/licenses/lzf/LICENSE.txt,sha256=17N6K9okXVGZuEoEz02RuJm0rWhwdsjgqS15K1If1GQ,1589
h5py-3.14.0.dist-info/top_level.txt,sha256=fO7Bsaa0F3Nx6djErCCbSw4-E7rBFMWrBVTGLEMxUMg,5
h5py/__init__.py,sha256=_rrHccfmxYOX14DlmFP9jyloa1IUTlg2FhuRIXN27ZE,3777
h5py/__pycache__/__init__.cpython-311.pyc,,
h5py/__pycache__/h5py_warnings.cpython-311.pyc,,
h5py/__pycache__/ipy_completer.cpython-311.pyc,,
h5py/__pycache__/version.cpython-311.pyc,,
h5py/_conv.cp311-win_amd64.pyd,sha256=Y_ZHsW_CFfrZOUSzbYLpnqUkRzoHVx0sXhwD8igqA-0,169472
h5py/_errors.cp311-win_amd64.pyd,sha256=wP1RkF9aqvUeSHEL-bNoR0-fXMnfEIzm6X9sfnJEvuI,46592
h5py/_hl/__init__.py,sha256=McVVGnMUXmMKA8GWPD9ie2jqe9qsgZ0rSYKmQIEN7C0,472
h5py/_hl/__pycache__/__init__.cpython-311.pyc,,
h5py/_hl/__pycache__/attrs.cpython-311.pyc,,
h5py/_hl/__pycache__/base.cpython-311.pyc,,
h5py/_hl/__pycache__/compat.cpython-311.pyc,,
h5py/_hl/__pycache__/dataset.cpython-311.pyc,,
h5py/_hl/__pycache__/datatype.cpython-311.pyc,,
h5py/_hl/__pycache__/dims.cpython-311.pyc,,
h5py/_hl/__pycache__/files.cpython-311.pyc,,
h5py/_hl/__pycache__/filters.cpython-311.pyc,,
h5py/_hl/__pycache__/group.cpython-311.pyc,,
h5py/_hl/__pycache__/selections.cpython-311.pyc,,
h5py/_hl/__pycache__/selections2.cpython-311.pyc,,
h5py/_hl/__pycache__/vds.cpython-311.pyc,,
h5py/_hl/attrs.py,sha256=aKR8KCrno5KmUPifq1s_IxcpJGq0Ut02lEVGx90Ug2M,10499
h5py/_hl/base.py,sha256=rvuVBKw5F3bblZUCZxhY5AHvaSE9EztnLxMKSKV3VW8,16279
h5py/_hl/compat.py,sha256=KSn38wDu5KOSEDImaOidHizTr0P-5eQwDahXzfspYaA,1654
h5py/_hl/dataset.py,sha256=nHqX3uRTl66UBTqaZq7BJdHCMDpYbZr2OtWVnd21wCU,44569
h5py/_hl/datatype.py,sha256=CKWF_DmX_A9afqf3anXcs0b-yC2p8_mcPIqS3alZB5U,1603
h5py/_hl/dims.py,sha256=aBBk4muIacpLhl6ra97wZx0v9U75KMBC9yqjm8Ajzq4,5290
h5py/_hl/files.py,sha256=RuOVdG7tLdpOMqDToyj3n-tJdVwd1DSBqOH_2xqEg2E,26051
h5py/_hl/filters.py,sha256=vW-spqNhHpIal7G9hC-wWDdYl9XxjDEM_KkkpQ-_oZA,14988
h5py/_hl/group.py,sha256=9Nm8wjjDmngODZ96j3fQ7jM7d2ZeJkdgGWqEMM6i2K0,31312
h5py/_hl/selections.py,sha256=1Kqm0tOTvMJt8Qf1cXKTXzXb15CjjOGbzy44u_4NDiw,15055
h5py/_hl/selections2.py,sha256=BSuUwex1Qfy1wN2Wb3dAD2vpnb8KXEoJRvLKG0GoAaI,2826
h5py/_hl/vds.py,sha256=6QmayV0ValjLUR1BtujYdSqz1TFZ6mahqm04c1x4Hdk,9718
h5py/_npystrings.cp311-win_amd64.pyd,sha256=Cwy6-hBabNyM01MatC77i68KKgzaLgXNaWF_DWs5k4g,49152
h5py/_objects.cp311-win_amd64.pyd,sha256=5pRyWh1erAnQ3JyskImslsk8SwHTgcAT6FpbIM5FNCQ,105984
h5py/_proxy.cp311-win_amd64.pyd,sha256=FsB8uiqnigetn3PBsN1i-9xFlwmJZxG9f9-aKPp4fIo,45056
h5py/_selector.cp311-win_amd64.pyd,sha256=EXHAKbzTIJAO7cXplZL704U-T-J0uyy-1IOmOOAof8k,125440
h5py/defs.cp311-win_amd64.pyd,sha256=H0xU1X1Uid1cKe7mDKSy-nMGLOJ5dmRfveClDhpwKIg,211968
h5py/h5.cp311-win_amd64.pyd,sha256=6sHBrAcL4sAC59nOB-iI4Rs7IZmyOFvXs4ntzeIIFFM,82944
h5py/h5a.cp311-win_amd64.pyd,sha256=J2XoQ9veJzoL737GOCshi0szRPzXW5a4RYte_UNIFw8,117760
h5py/h5ac.cp311-win_amd64.pyd,sha256=GrFxcPK0pTvI4k6d0QQUL4LG9xP_KMJ5I3wLG7BL1bc,52736
h5py/h5d.cp311-win_amd64.pyd,sha256=5kxza8Us_u65vhuE6rgL1zFBTOmHgZo9Dx4Hk1OBo2I,232960
h5py/h5ds.cp311-win_amd64.pyd,sha256=tqfBWqJ_etxXOn4zFCnwqfQ2skmePzGsXdyztcZzAEg,73728
h5py/h5f.cp311-win_amd64.pyd,sha256=sBcq6aCbgkF4SGNB32iP_Klq1suBn5mubPAz2NdRTGk,130560
h5py/h5fd.cp311-win_amd64.pyd,sha256=F96EkDWfUeCAoNjpDkRnPsoRHroRBPMqVBCDrlbS7tI,136192
h5py/h5g.cp311-win_amd64.pyd,sha256=5sX_M34p1kt7tEz8x3KzUdujPLtzjU-0xXT6w9N5Sbc,138240
h5py/h5i.cp311-win_amd64.pyd,sha256=c-UN2tVffq-YhbDIjj2BJc8x-ZALMnUt81pUC0NGZXE,54784
h5py/h5l.cp311-win_amd64.pyd,sha256=ClitD3d8HYzQvKim7JFdd4D7lTekyT_pd0z7CoDnpgM,96768
h5py/h5o.cp311-win_amd64.pyd,sha256=2pr0uljm8wIYB6KvXLwCL29pJklKWslngRlzntcioZs,115712
h5py/h5p.cp311-win_amd64.pyd,sha256=iHrza63Rd2vAPyi5wivp92G_kT7yRN9l-Z6R-QAukvc,339968
h5py/h5pl.cp311-win_amd64.pyd,sha256=BSOZIP0t2yt01vvFGrXTR-Z3o-QHkUHaXTc6G93YN5A,45568
h5py/h5py_warnings.py,sha256=YR2DLMumy_qgchAg5_MLJniloT90Dil5_OHftF7MmD8,544
h5py/h5r.cp311-win_amd64.pyd,sha256=SG4SfKiVKp3OarUkb1g8eoD_L5BaehWMxQqJYyJWjAw,63488
h5py/h5s.cp311-win_amd64.pyd,sha256=ntznTuSAFp1GGkHSukR3r1zaYanaU09Pf4YvduQC4QA,111616
h5py/h5t.cp311-win_amd64.pyd,sha256=urt6LNwd6LrzfuUzJkG66efBd0JFYpF1aSYucq-2Zs8,321024
h5py/h5z.cp311-win_amd64.pyd,sha256=dV9JOS4m6hNLnLi-NDzX_zbyj8BWXIIGsox1kzOvsO0,50176
h5py/hdf5.dll,sha256=YNFb_4U6DqLwUGg4UzKalivLPBTZHdL9q0oCojaZd4A,3330560
h5py/hdf5_hl.dll,sha256=IMRg7KwlPGGC3zmkYsRpSv5IYAJtIqDqspCxj5GR3jw,121344
h5py/ipy_completer.py,sha256=iOKbnKizsvTlbsVU4ArkZHLWLqJfbjsrKMfLKYgQSGo,3889
h5py/tests/__init__.py,sha256=fLCW7Dm0BboDAVY-KDWdK_GRPPJv-KW7Z31kiKOaCtI,691
h5py/tests/__pycache__/__init__.cpython-311.pyc,,
h5py/tests/__pycache__/common.cpython-311.pyc,,
h5py/tests/__pycache__/conftest.cpython-311.pyc,,
h5py/tests/__pycache__/test_attribute_create.cpython-311.pyc,,
h5py/tests/__pycache__/test_attrs.cpython-311.pyc,,
h5py/tests/__pycache__/test_attrs_data.cpython-311.pyc,,
h5py/tests/__pycache__/test_base.cpython-311.pyc,,
h5py/tests/__pycache__/test_big_endian_file.cpython-311.pyc,,
h5py/tests/__pycache__/test_completions.cpython-311.pyc,,
h5py/tests/__pycache__/test_dataset.cpython-311.pyc,,
h5py/tests/__pycache__/test_dataset_getitem.cpython-311.pyc,,
h5py/tests/__pycache__/test_dataset_swmr.cpython-311.pyc,,
h5py/tests/__pycache__/test_datatype.cpython-311.pyc,,
h5py/tests/__pycache__/test_dimension_scales.cpython-311.pyc,,
h5py/tests/__pycache__/test_dims_dimensionproxy.cpython-311.pyc,,
h5py/tests/__pycache__/test_dtype.cpython-311.pyc,,
h5py/tests/__pycache__/test_errors.cpython-311.pyc,,
h5py/tests/__pycache__/test_file.cpython-311.pyc,,
h5py/tests/__pycache__/test_file2.cpython-311.pyc,,
h5py/tests/__pycache__/test_file_alignment.cpython-311.pyc,,
h5py/tests/__pycache__/test_file_image.cpython-311.pyc,,
h5py/tests/__pycache__/test_filters.cpython-311.pyc,,
h5py/tests/__pycache__/test_group.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5d_direct_chunk.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5f.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5o.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5p.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5pl.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5s.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5t.cpython-311.pyc,,
h5py/tests/__pycache__/test_h5z.cpython-311.pyc,,
h5py/tests/__pycache__/test_npystrings.cpython-311.pyc,,
h5py/tests/__pycache__/test_objects.cpython-311.pyc,,
h5py/tests/__pycache__/test_ros3.cpython-311.pyc,,
h5py/tests/__pycache__/test_selections.cpython-311.pyc,,
h5py/tests/__pycache__/test_slicing.cpython-311.pyc,,
h5py/tests/common.py,sha256=exoIAp_l0nZhZLXZgO9wGTsN5ys23gpj1QffpREwXIg,8006
h5py/tests/conftest.py,sha256=OiQZ2XuYVfeikYVoHGIuOkgXgsaKPpbTpfB6rY07eoA,569
h5py/tests/data_files/__init__.py,sha256=mqVluyvrAZ6HEZH0QAX8D8q-ErxJG_b3roDk1UMh_RU,200
h5py/tests/data_files/__pycache__/__init__.cpython-311.pyc,,
h5py/tests/data_files/vlen_string_dset.h5,sha256=kA-LrxnT2MTRGTrBrGAZ7nd0AF6FwDCvROZ1ezjSl5M,6304
h5py/tests/data_files/vlen_string_dset_utc.h5,sha256=hbcoOCuDPB2mFie5ozTiKCLVwcs1n-O6byUmKvRTL2M,169904
h5py/tests/data_files/vlen_string_s390x.h5,sha256=6pkMaOA3G6-rSWb3UvoTVbO59kNgBm9tna_48bfnTKU,9008
h5py/tests/test_attribute_create.py,sha256=liDSJwp2td9I6jsEV0xXc6YNdpK4U2NU4zh8cDfQHYA,3085
h5py/tests/test_attrs.py,sha256=fLUZ7E-xsgh6Z8RLKn-yabUMb2ojt9ijM_rWHjD8M-E,9696
h5py/tests/test_attrs_data.py,sha256=gSUOwHyBwCTmROTP6jiBdUEnssipWindpfyO378unBc,10079
h5py/tests/test_base.py,sha256=QTxwpyHIJaT1NXEV95ApbEikEtzh5ZqMfD6bSNTUuSw,3962
h5py/tests/test_big_endian_file.py,sha256=oPoetdkfWeGLSAMpEGagpRQC7VbIZplkU9sLKyxR0OM,1497
h5py/tests/test_completions.py,sha256=jA2OuWaYhcPr4fAi04j6D3DjgCdY-BIzJquCQHa2Hvo,1525
h5py/tests/test_dataset.py,sha256=CWx4dTnXs5Upmxl9fY_wTgOOKLiB2Z5FhymRV7FTqE0,83352
h5py/tests/test_dataset_getitem.py,sha256=FLWDesUVsY-SsQsnKOY_3gaIhP0znUJbUryHXkgiGU0,19339
h5py/tests/test_dataset_swmr.py,sha256=XDzSDzCKIXZnoGkRUnin3hjU4nPfIiyJuBBIBj1bMxk,4093
h5py/tests/test_datatype.py,sha256=Ed8zcEv-0UahPxM4fbF3SVKHdZbrUscOTOM1MVb0Fgw,1047
h5py/tests/test_dimension_scales.py,sha256=ydkP35UlA6RAjvuqZQe2utP1weWZcTvlnwNn4rm3Rec,8334
h5py/tests/test_dims_dimensionproxy.py,sha256=mZ008MxVRhtf3WCR_pwLn527VU2NVo-i1WDYAGTF5BI,625
h5py/tests/test_dtype.py,sha256=wqvYS5HxmdbfEwonZBq1KJaIMjbE3RIwPeL5lYIaoyQ,18433
h5py/tests/test_errors.py,sha256=eA68-lYML0_CJNHNB6uH6OuZeITEOXUQ7JYoZ9DmVCI,2331
h5py/tests/test_file.py,sha256=cnMpNMh3EYQr1YCgDNSp1MwguvBvEBi88fFXn5DJbi4,35510
h5py/tests/test_file2.py,sha256=MZZRKtjDRrljByYvhDXJBWV5-WkMGhmtxvpx5wsd0Tk,11020
h5py/tests/test_file_alignment.py,sha256=aA5fB1dzJwyzA5aEagJq8NzxnUBJGvG4grc2JUc_xEo,4509
h5py/tests/test_file_image.py,sha256=f_4zTwZ1YMD3OU7XURcPWA6VasylORPkw5QuydOSQWk,2067
h5py/tests/test_filters.py,sha256=SJiYJi9wxN0C34WlQXvEEbxzv_Dxf1Dj9lGOmU0hyt0,3161
h5py/tests/test_group.py,sha256=g2r_PldI7MliNj7HqipIvQ91XnuHbXjYWi8Qc1uhg1U,40798
h5py/tests/test_h5.py,sha256=tUVpBOgKSVHLlDpu3urVcmaBPMt0vxAr46nIEgL4an4,1261
h5py/tests/test_h5d_direct_chunk.py,sha256=0Viao2ohJn-0MnwAinOyMhScSmosQ9e1Lr9Shohd4cg,7931
h5py/tests/test_h5f.py,sha256=iQGz7YI01xUDBAsFC7ZwZmx6hb7_Q97Zf0cfdnKjZZ4,4127
h5py/tests/test_h5o.py,sha256=j5cc1FGgSPoQgpxXC-ETSzQ32nEFuZDmKfovzjuk7Mc,529
h5py/tests/test_h5p.py,sha256=dcpP0FPpsXzTXJNm8nMoWw647MPVwExlgoDzQVlQaOM,7042
h5py/tests/test_h5pl.py,sha256=qtcjqJy99yo3GWmrRcJUFf5Pl_2rPNNpPkl-SrfnuEM,1859
h5py/tests/test_h5s.py,sha256=835CjwaM8WEckbJvqaShwAB4icFZw1sBlsWTRVsAp-g,758
h5py/tests/test_h5t.py,sha256=Uhg1w6WfOkPWH0iUBfO5Onlw2O61sUiYvsL0MixSwrs,6770
h5py/tests/test_h5z.py,sha256=2w_IFh8o2B2kIlXXMq4uc9aEppWUwkAGJX80ogs0Rbs,2004
h5py/tests/test_npystrings.py,sha256=3NcvXEE13ZVjiXDLVZeaAbE3ZFsB-l6LG60e9EuES-s,5224
h5py/tests/test_objects.py,sha256=0ty2KAyj_YluKnL-Nuv4iSMIa5h573IzBHBWDQCHAPQ,5915
h5py/tests/test_ros3.py,sha256=ttNSNiiZTvv66Fi6rdTP6c4YVlqxfPj2Njs-806hWE0,2234
h5py/tests/test_selections.py,sha256=O4ZY5XX5oJDzQayTtgD-f3Hn7kMlZZKUFRVX4RDU42s,4995
h5py/tests/test_slicing.py,sha256=8OyyTYkOAycRJbJsCjJ5_GDbdDKT4kVDjdwocD66nZY,14290
h5py/tests/test_vds/__init__.py,sha256=I2uUocf1v8DxK1ox18-rp4bt1rWkCFXLL32K6rtGZcU,107
h5py/tests/test_vds/__pycache__/__init__.cpython-311.pyc,,
h5py/tests/test_vds/__pycache__/test_highlevel_vds.cpython-311.pyc,,
h5py/tests/test_vds/__pycache__/test_lowlevel_vds.cpython-311.pyc,,
h5py/tests/test_vds/__pycache__/test_virtual_source.cpython-311.pyc,,
h5py/tests/test_vds/test_highlevel_vds.py,sha256=TfpiYcLcF4O7jPg_dFkA5h1RNpX0A8QJ1xzHr4o9T54,18038
h5py/tests/test_vds/test_lowlevel_vds.py,sha256=Z5M8YCAIUyu6-cPcsiOL65UT_oD7zuMVuFgBhiFqSmo,12163
h5py/tests/test_vds/test_virtual_source.py,sha256=YF-uoptLTweL0gAKQaWhBNAgt2DVWH9MetPTgC6Bedw,6199
h5py/utils.cp311-win_amd64.pyd,sha256=_qIGQuoxgmTAbWiC1f0PVnXhaJoIA8zxgM00gxhSdiI,55296
h5py/version.py,sha256=D0AHZNQMTjqaW-xN0CMXHUumhWuTNjMc5LYoLgpI6RE,1976
h5py/zlib.dll,sha256=jgKgB_0sD64jXgwuQF1YO9srBZFi6hotwpe2kLt8N8I,86016
